import { createFeedbackManager } from '../../utils/feedbackManager.js'
import { handleError, getLoadingManager } from '../../utils/errorHandler.js'
import { FEEDBACK_TYPES } from '../../constants/index.js'
import subscribeManager, { TEMPLATE_TYPES } from '../../utils/subscribeManager.js'

const feedbackManager = createFeedbackManager(FEEDBACK_TYPES.REPAIR)
const loadingManager = getLoadingManager()

Page({
  data: {
    typeOptions: [],
    typeSelectedIndex: -1,
    form: {
      type: '',
      typeLabel: '',
      content: '',
      mediaUrls: [],
      address: '',
      name: '',
      phone: ''
    },
    loading: false,
    contentError: '',
    businessId: null // 业务ID
  },

  onLoad() {
    this.initPage()
  },

  // 初始化页面数据
  initPage() {
    const typeOptions = feedbackManager.getTypeOptions()
    const prefilledForm = feedbackManager.getPrefilledForm()
    const businessId = feedbackManager.generateBusinessId()

    this.setData({
      typeOptions,
      form: prefilledForm,
      businessId
    })
  },

  // 选择类型
  onTypeChange(e) {
    const index = e.detail.value
    const selectedType = this.data.typeOptions[index]

    if (selectedType) {
      this.setData({
        typeSelectedIndex: index,
        'form.type': selectedType.value,
        'form.typeLabel': selectedType.label
      })
    }
  },

  // 输入内容
  onContentInput(e) {
    const value = e.detail
    let contentError = ''
    if (!value || value.trim().length < 10) {
      contentError = '请输入至少10个字符的反馈内容'
    }
    this.setData({
      'form.content': value,
      contentError
    })
  },

  // 输入地址
  onAddressInput(e) {
    this.setData({
      'form.address': e.detail
    })
  },

  // 输入姓名
  onNameInput(e) {
    this.setData({
      'form.name': e.detail
    })
  },

  // 输入电话
  onPhoneInput(e) {
    this.setData({
      'form.phone': e.detail
    })
  },

  // van-uploader 上传后回调
  async onMediaUpload(e) {
    const { file } = e.detail
    const newFiles = await feedbackManager.handleFileUpload(
      file,
      this.data.form.mediaUrls,
      loadingManager,
      handleError,
      this.data.businessId // 传递业务ID
    )
    this.setData({ 'form.mediaUrls': newFiles })
  },

  // van-uploader 删除文件回调
  async onMediaDelete(e) {
    const { index } = e.detail
    const updatedFiles = await feedbackManager.handleFileDelete(index, this.data.form.mediaUrls)
    this.setData({ 'form.mediaUrls': updatedFiles })
  },



  // 提交表单
  async onSubmit() {
    if (this.data.loading) return

    const validation = feedbackManager.validateForm(this.data.form)
    if (!validation.valid) {
      this.setData({ contentError: validation.message })
      wx.showToast({
        title: validation.message,
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })
    loadingManager.show('提交中...')

    try {
      const formDataWithId = {
        ...this.data.form,
        businessId: this.data.businessId // 传递业务ID
      }
      const result = await feedbackManager.submitFeedback(formDataWithId)

      if (result.success) {
        wx.showToast({
          title: '提交成功',
          icon: 'success'
        })

        // 请求订阅授权
        this.requestSubscribeAfterSubmit()

        // 延迟跳转到历史页面
        setTimeout(() => {
          this.goToHistory()
        }, 1500)
      } else {
        throw new Error(result.error)
      }

    } catch (error) {
      handleError(error, '提交报修')
    } finally {
      this.setData({ loading: false })
      loadingManager.hide()
    }
  },

  // 跳转到历史记录
  goToHistory() {
    wx.redirectTo({
      url: '/pages/bx/history'
    })
  },

  // 提交成功后请求订阅授权
  async requestSubscribeAfterSubmit() {
    try {
      // 延迟一点时间，避免与成功提示冲突
      setTimeout(async () => {
        try {
          const result = await subscribeManager.smartSubscribeCheck(
            TEMPLATE_TYPES.REPAIR_NOTICE,
            {
              title: '报修通知',
              content: '订阅报修通知后，您将及时收到报修进度更新消息',
              autoRequest: true
            }
          )

          if (result) {
            console.log('[BX] 用户已订阅报修通知')
          } else {
            console.log('[BX] 用户未订阅报修通知')
          }
        } catch (error) {
          console.warn('[BX] 订阅请求失败:', error)
        }
      }, 800) // 延迟800毫秒
    } catch (error) {
      console.warn('[BX] 请求订阅授权失败:', error)
    }
  }
});