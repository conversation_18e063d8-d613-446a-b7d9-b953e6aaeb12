import fileAccessManager from '../../utils/fileAccessManager.js'

const app = getApp();

Page({
  data: {
    detail: null,
    loading: true,
    timeDiff: ''
  },

  onLoad(options) {
    const { id } = options;
    if (!id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    this.getDetail(id);
  },

  // 获取投诉建议详情
  async getDetail(id) {
    this.setData({ loading: true });
    try {
      const res = await app.request({
        url: '/api/wx/complaint/detail',
        method: 'POST',
        data: { id }
      });

      if (res.code === 0 && res.data) {
        const detail = this.processDetailData(res.data);
        const timeDiff = this.calculateTimeDiff(detail.create_time);
        this.setData({
          detail,
          timeDiff,
          loading: false
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: '投诉建议详情'
        });
      } else {
        throw new Error(res.msg || '获取详情失败');
      }
    } catch (error) {
      console.error('获取投诉建议详情失败:', error);
      wx.showToast({
        title: '获取详情失败',
        icon: 'none'
      });
      this.setData({ loading: false });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 处理详情数据
  processDetailData(data) {
    let mediaUrls = [];
    let imageUrls = [];
    let nonImageUrls = [];

    try {
      if (data.media_urls) {
        const parsed = JSON.parse(data.media_urls);
        mediaUrls = parsed.map(media => {
          const url = media.url || media;
          const fileId = media.fileId;
          // 生成下载URL：优先使用fileId，否则使用原URL
          const downloadUrl = fileId ? `${app.globalData.baseUrl}/fv/download/${fileId}` : url;
          return {
            url: url,
            fileId: fileId,
            downloadUrl: downloadUrl,
            thumb: media.thumb || downloadUrl,
            name: media.name || '附件',
            size: media.size || 0,
            sizeText: this.formatFileSize(media.size || 0),
            isImage: this.isImage(url)
          };
        });

        // 分离图片和非图片附件
        imageUrls = mediaUrls.filter(media => media.isImage);
        nonImageUrls = mediaUrls.filter(media => !media.isImage);
      }
    } catch (e) {
      console.warn('解析media_urls失败:', e);
      mediaUrls = [];
      imageUrls = [];
      nonImageUrls = [];
    }

    return {
      ...data,
      mediaUrls,
      imageUrls,
      nonImageUrls,
      statusText: this.getStatusText(data.status),
      statusColor: this.getStatusColor(data.status)
    };
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      '0': '待处理',
      '1': '处理中',
      '2': '已完成'
    };
    return statusMap[status] || '待处理';
  },

  // 获取状态颜色
  getStatusColor(status) {
    const colorMap = {
      '0': '#ff4d4f',  // 红色
      '1': '#faad14',  // 橙色
      '2': '#52c41a'   // 绿色
    };
    return colorMap[status] || '#ff4d4f';
  },

  // 计算时间差
  calculateTimeDiff(createTime) {
    if (!createTime) return '';
    
    try {
      const now = new Date();
      const create = new Date(createTime);
      const diff = now - create;
      
      const minutes = Math.floor(diff / (1000 * 60));
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      
      if (minutes < 60) {
        return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;
      } else if (hours < 24) {
        return `${hours}小时前`;
      } else if (days < 30) {
        return `${days}天前`;
      } else {
        return '';
      }
    } catch (e) {
      return '';
    }
  },

  // 判断是否为图片
  isImage(url) {
    if (!url) return false;
    const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    const lowerUrl = url.toLowerCase();
    return imageExts.some(ext => lowerUrl.includes(ext));
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // 预览图片
  onPreviewImage(e) {
    const { url, item } = e.currentTarget.dataset;
    const urls = item.imageUrls.map(img => img.downloadUrl);
    wx.previewImage({
      current: url,
      urls: urls
    });
  },

  // 下载文件
  async onDownloadFile(e) {
    const { url, name, fileId, id } = e.currentTarget.dataset;

    try {
      wx.showLoading({ title: '下载中...' });

      // 优先使用fileId，然后是id，最后降级使用url
      const actualFileId = fileId || id;

      // 使用新的智能预览方法，基于文件ID
      await fileAccessManager.previewFile(actualFileId, url, name);

      wx.showToast({
        title: '打开成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('文件下载/打开失败:', error);
      wx.showToast({
        title: '下载失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  }
});
