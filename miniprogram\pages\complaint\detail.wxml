<!-- 全局水印 -->
<global-watermark showWatermark="{{showWatermark}}" watermarkConfig="{{watermarkConfig}}"></global-watermark>

<view class="detail-page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <van-loading type="spinner" size="24px">加载中...</van-loading>
  </view>

  <!-- 详情内容 -->
  <view wx:elif="{{detail}}" class="detail-content">
    <!-- 标题区域 -->
    <view class="detail-header">
      <view class="header-left">
        <text class="location-icon">💬</text>
        <text class="address">投诉建议</text>
        <text class="time-diff">{{timeDiff}}</text>
      </view>
      <view class="status-tag" style="color: {{detail.statusColor}};">
        {{detail.statusText}}
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="info-section">
      <view class="section-title">基本信息</view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">反馈人：</text>
          <text class="info-value">{{detail.name || '未填写'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">联系地址：</text>
          <text class="info-value">{{detail.address || '未填写地址'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">联系电话：</text>
          <text class="info-value">{{detail.phone || '未填写'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">反馈时间：</text>
          <text class="info-value">{{detail.create_time}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">类型：</text>
          <text class="info-value">{{detail.type || '投诉建议'}}</text>
        </view>
      </view>
    </view>

    <!-- 反馈内容 -->
    <view class="content-section">
      <view class="content-title">反馈内容</view>
      <text class="content-text">{{detail.content}}</text>
    </view>

    <!-- 图片展示 -->
    <view wx:if="{{detail.imageUrls && detail.imageUrls.length}}" class="images-section">
      <view class="section-title">相关图片</view>
      <view class="images-grid">
        <view
          class="image-item"
          wx:for="{{detail.imageUrls}}"
          wx:for-item="image"
          wx:key="url"
          data-url="{{image.downloadUrl}}"
          data-item="{{detail}}"
          bindtap="onPreviewImage"
        >
          <image
            class="image"
            src="{{image.downloadUrl}}"
            mode="aspectFill"
            lazy-load
          />
        </view>
      </view>
    </view>

    <!-- 附件展示 -->
    <view wx:if="{{detail.nonImageUrls && detail.nonImageUrls.length}}" class="attachments-section">
      <view class="section-title">相关附件</view>
      <view class="attachments-list">
        <view
          class="attachment-item"
          wx:for="{{detail.nonImageUrls}}"
          wx:for-item="media"
          wx:key="fileId"
          data-url="{{media.downloadUrl}}"
          data-name="{{media.name}}"
          data-file-id="{{media.fileId}}"
          data-id="{{media.fileId}}"
          bindtap="onDownloadFile"
        >
          <view class="file-icon">📄</view>
          <view class="attachment-info">
            <text class="attachment-name">{{media.name}}</text>
            <text wx:if="{{media.sizeText}}" class="attachment-size">{{media.sizeText}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 处理结果独立内容块 -->
  <view wx:if="{{detail}}" class="result-content">
    <!-- 处理进展 -->
    <view wx:if="{{detail.status == 2}}" class="result-section">
      <view class="section-title-top">处理进展</view>
      <view class="info-list">
        <view wx:if="{{detail.handler}}" class="info-item">
          <text class="info-label">处理人：</text>
          <text class="info-value">{{detail.handler}}</text>
        </view>
        <view wx:if="{{detail.handling_time}}" class="info-item">
          <text class="info-label">处理时间：</text>
          <text class="info-value">{{detail.handling_time}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">处理反馈：</text>
          <text class="info-value {{detail.reply_content ? '' : 'no-feedback'}}">{{detail.reply_content || '暂无处理反馈'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 错误状态 -->
  <view wx:else class="error">
    <text>加载失败，请重试</text>
  </view>
</view>
