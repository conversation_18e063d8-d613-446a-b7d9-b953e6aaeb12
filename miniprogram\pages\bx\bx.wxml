<!-- 全局水印 -->
<global-watermark showWatermark="{{showWatermark}}" watermarkConfig="{{watermarkConfig}}"></global-watermark>

<!-- 页面头部 -->
<view class="page-header">
  <view class="header-content">
    <view class="header-title">
      <text class="title-main">报事报修</text>
      <text class="title-sub">快速反馈您遇到的问题</text>
    </view>
    <view class="header-action">
      <text class="history-link" bindtap="goToHistory">
        <text class="history-icon">📋</text>反馈历史
      </text>
    </view>
  </view>
</view>

<!-- 主要内容区域 -->
<view class="form-container">
  <!-- 基本信息卡片 -->
  <view class="card-section">
    <view class="card-header">
      <text class="card-icon">🔧</text>
      <text class="card-title">基本信息</text>
    </view>
    <view class="card-content">
      <view class="form-item">
        <view class="label-row">
          <text class="label">报事报修项<text class="required">*</text></text>
        </view>
        <picker
          mode="selector"
          range="{{typeOptions}}"
          range-key="label"
          value="{{typeSelectedIndex}}"
          bind:change="onTypeChange"
          custom-class="type-picker"
        >
          <van-cell
            title="报事报修项"
            value="{{form.typeLabel || '请选择'}}"
            is-link
            required
            custom-class="type-selector"
          />
        </picker>
      </view>
      <view class="form-item">
        <van-field
          label="报事报修内容"
          required
          type="textarea"
          placeholder="请详细描述您遇到的问题，以便我们更好地为您服务"
          value="{{form.content}}"
          bind:change="onContentInput"
          autosize="{{ {minHeight: 120, maxHeight: 300} }}"
          maxlength="500"
          show-word-limit
        />
      </view>
    </view>
  </view>

  <!-- 附件上传卡片 -->
  <view class="card-section">
    <view class="card-header">
      <text class="card-icon">📷</text>
      <text class="card-title">添加图片</text>
      <text class="card-subtitle">最多可上传3张图片</text>
    </view>
    <view class="card-content">
      <view class="form-item">
        <van-uploader
          file-list="{{ form.mediaUrls }}"
          bind:after-read="onMediaUpload"
          bind:delete="onMediaDelete"
          max-count="3"
          accept="image"
          multiple
          size-type="{{ ['compressed'] }}"
          max-size="{{ 10 * 1024 * 1024 }}"
          isImage
          preview-image
          preview-full-image
        />
      </view>
    </view>
  </view>

  <!-- 联系信息卡片 -->
  <view class="card-section">
    <view class="card-header">
      <text class="card-icon">👤</text>
      <text class="card-title">联系信息</text>
    </view>
    <view class="card-content">
      <van-field
        label="事发地址"
        required
        placeholder="请输入详细地址"
        value="{{form.address}}"
        bind:change="onAddressInput"
      />
      <van-field
        label="姓名"
        placeholder="请输入姓名"
        value="{{form.name}}"
        bind:change="onNameInput"
      />
      <van-field
        label="联系电话"
        placeholder="请输入联系电话"
        value="{{form.phone}}"
        bind:change="onPhoneInput"
        type="number"
      />
    </view>
  </view>
</view>

<!-- 提交按钮 -->
<view class="submit-container">
  <van-button
    type="primary"
    size="large"
    loading="{{loading}}"
    bind:click="onSubmit"
    custom-class="submit-btn"
  >
    提交报修申请
  </van-button>
</view>