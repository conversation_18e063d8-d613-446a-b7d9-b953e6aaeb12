const app = getApp();
import fileAccessManager from '../../utils/fileAccessManager.js';

Page({
  data: {
    list: [],
    loading: true
  },
  onLoad() {
    this.getHistory();
  },
  async getHistory() {
    this.setData({ loading: true });
    try {
      const res = await app.request({
        url: '/api/wx/complaint/history',
        method: 'POST'
      });
      if (res.code === 0) {
        const list = (res.data || []).map(item => {
          let reply_content = '';
          if (item.status == 2) {
            reply_content = item.reply_content || '';
          }

          let mediaUrls = [];
          let imageUrls = [];
          let nonImageUrls = [];
          try {
            if (item.media_urls) {
              const parsed = JSON.parse(item.media_urls);
              // 处理新格式的附件数据（包含fileId等信息）
              mediaUrls = parsed.map(media => {
                const url = media.url || media;
                const fileId = media.fileId;
                // 生成下载URL：优先使用fileId，否则使用原URL
                const downloadUrl = fileId ? `${app.globalData.baseUrl}/fv/download/${fileId}` : url;
                return {
                  url: url,
                  fileId: fileId,
                  downloadUrl: downloadUrl,
                  thumb: media.thumb || downloadUrl,
                  name: media.name || '附件',
                  size: media.size || 0,
                  sizeText: this.formatFileSize(media.size || 0),
                  isImage: this.isImage(url)
                };
              });

              // 分离图片和非图片附件
              imageUrls = mediaUrls.filter(media => media.isImage);
              nonImageUrls = mediaUrls.filter(media => !media.isImage);
            }
          } catch (e) {
            console.warn('解析media_urls失败:', e);
            mediaUrls = [];
            imageUrls = [];
            nonImageUrls = [];
          }

          // 计算时间差
          const timeDiff = this.calculateTimeDiff(item.create_time);

          return {
            ...item,
            mediaUrls,
            imageUrls,
            nonImageUrls,
            statusText: this.getStatusText(item.status),
            statusColor: this.getStatusColor(item.status),
            timeDiff,
            reply_content
          };
        });
        this.setData({ list, loading: false });
      } else {
        this.setData({ list: [], loading: false });
      }
    } catch (e) {
      this.setData({ list: [], loading: false });
    }
  },

  // 格式化文件大小
  formatFileSize(size) {
    if (!size || size === 0) return '';
    if (size < 1024) return size + 'B';
    if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB';
    return (size / 1024 / 1024).toFixed(1) + 'MB';
  },

  // 附件点击处理
  onAttachmentTap(e) {
    const { url } = e.currentTarget.dataset;

    if (this.isImage(url)) {
      this.onPreviewImage(e);
    } else {
      this.onDownloadFile(e);
    }
  },

  // 预览图片
  onPreviewImage(e) {
    const { url } = e.currentTarget.dataset;
    const { item } = e.currentTarget.dataset;

    // 提取所有图片URL用于预览
    const imageUrls = item.mediaUrls
      .filter(media => this.isImage(media.url))
      .map(media => media.url);

    if (imageUrls.length > 0) {
      wx.previewImage({
        current: url,
        urls: imageUrls
      });
    }
  },

  // 判断是否为图片
  isImage(url) {
    const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    const lowerUrl = url.toLowerCase();
    return imageExts.some(ext => lowerUrl.includes(ext));
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      '0': '待处理',
      '1': '处理中',
      '2': '已完成'
    };
    return statusMap[status] || '待处理';
  },

  // 获取状态颜色
  getStatusColor(status) {
    const colorMap = {
      '0': '#ff4d4f',  // 红色
      '1': '#faad14',  // 橙色
      '2': '#52c41a'   // 绿色
    };
    return colorMap[status] || '#ff4d4f';
  },

  // 计算时间差
  calculateTimeDiff(createTime) {
    if (!createTime) return '';

    try {
      const now = new Date();
      const create = new Date(createTime);
      const diff = now - create;

      const minutes = Math.floor(diff / (1000 * 60));
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));

      if (minutes < 60) {
        return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;
      } else if (hours < 24) {
        return `${hours}小时前`;
      } else if (days < 30) {
        return `${days}天前`;
      } else {
        return '';
      }
    } catch (e) {
      return '';
    }
  },

  // 预览图片
  onPreviewImage(e) {
    const { url, item } = e.currentTarget.dataset;
    const urls = item.imageUrls.map(img => img.downloadUrl);
    wx.previewImage({
      current: url,
      urls: urls
    });
  },

  // 跳转到详情页面
  goToDetail(e) {
    const { id } = e.currentTarget.dataset;
    if (!id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/complaint/detail?id=${id}`
    });
  },

  // 下载文件
  async onDownloadFile(e) {
    const { url, name, fileId, id } = e.currentTarget.dataset;

    try {
      wx.showLoading({ title: '下载中...' });

      // 优先使用fileId，然后是id，最后降级使用url
      const actualFileId = fileId || id;

      // 使用新的智能预览方法，基于文件ID
      await fileAccessManager.previewFile(actualFileId, url, name);

      wx.showToast({
        title: '打开成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('文件下载/打开失败:', error);
      wx.showToast({
        title: '下载失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  onPullDownRefresh() {
    this.getHistory().then(() => wx.stopPullDownRefresh());
  }
});