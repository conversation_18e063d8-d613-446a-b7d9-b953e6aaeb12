<!-- 全局水印 -->
<global-watermark showWatermark="{{showWatermark}}" watermarkConfig="{{watermarkConfig}}"></global-watermark>

<view class="detail-page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <van-loading type="spinner" size="24px">加载中...</van-loading>
  </view>

  <!-- 详情内容 -->
  <view wx:elif="{{detail}}" class="detail-content">
    <!-- 标题区域 -->
    <view class="detail-header">
      <view class="header-left">
        <text class="location-icon">🏠</text>
        <text class="address">报修工单</text>
        <text class="time-diff">{{timeDiff}}</text>
      </view>
      <view class="status-tag" style="color: {{detail.statusColor}};">
        {{detail.statusText}}
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="info-section">
      <view class="section-title">基本信息</view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">报修人：</text>
          <text class="info-value">{{detail.name || '未填写'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">报修地址：</text>
          <text class="info-value">{{detail.address || '未填写地址'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">联系电话：</text>
          <text class="info-value">{{detail.phone || '未填写'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">报修时间：</text>
          <text class="info-value">{{detail.create_time}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">类型：</text>
          <text class="info-value">{{detail.type || '公共报修'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">报修内容：</text>
          <text class="info-value content-value">{{detail.content}}</text>
        </view>
      </view>
    </view>

    <!-- 图片区域 -->
    <view wx:if="{{detail.mediaUrls && detail.mediaUrls.length}}" class="images-section">
      <view class="section-title">相关图片</view>
      <view class="images-grid">
        <view
          class="image-item"
          wx:for="{{detail.mediaUrls}}"
          wx:key="url"
          wx:if="{{item.isImage}}"
          data-url="{{item.downloadUrl}}"
          data-file-id="{{item.fileId}}"
          data-name="{{item.name}}"
          bindtap="onAttachmentTap"
        >
          <image
            class="image"
            src="{{item.downloadUrl}}"
            mode="aspectFill"
            lazy-load
          />
        </view>
      </view>
    </view>

  </view>

  <!-- 处理结果独立内容块 -->
  <view wx:if="{{detail}}" class="result-content">
    <!-- 处理进展 -->
    <view wx:if="{{detail.status == 2 || detail.status == 3}}" class="result-section">
      <view class="section-title-top">处理进展</view>
      <view class="info-list">
        <view wx:if="{{detail.handler}}" class="info-item">
          <text class="info-label">处理人：</text>
          <text class="info-value">{{detail.handler}}</text>
        </view>
        <view wx:if="{{detail.handling_time}}" class="info-item">
          <text class="info-label">处理时间：</text>
          <text class="info-value">{{detail.handling_time}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">处理反馈：</text>
          <text class="info-value {{detail.reply_content ? '' : 'no-feedback'}}">{{detail.reply_content || '暂无处理反馈'}}</text>
        </view>
        <!-- 验收信息 - 只有工单状态为已完成(2)时才显示验收相关信息 -->
        <view wx:if="{{detail.showYsInfo}}" class="info-item">
          <text class="info-label">验收状态：</text>
          <text class="info-value ys-status-{{detail.ys_status}}">{{detail.ysStatusText}}</text>
        </view>
        <view wx:if="{{detail.showYsInfo && (detail.ys_status == 1 || detail.ys_status == 2)}}" class="info-item">
          <text class="info-label">验收时间：</text>
          <text class="info-value">{{detail.ys_time}}</text>
        </view>
        <view wx:if="{{detail.showYsInfo && (detail.ys_status == 1 || detail.ys_status == 2) && detail.ys_reply}}" class="info-item">
          <text class="info-label">验收意见：</text>
          <text class="info-value">{{detail.ys_reply}}</text>
        </view>
      </view>
    </view>

    <!-- 暂无处理结果提示 -->
    <view wx:elif="{{detail.status != 2 && detail.status != 3}}" class="no-reply-section">
      <view class="section-title-top">处理状态</view>
      <view class="no-reply-content">
        <text class="no-reply-text">{{detail.statusText}}，请耐心等待处理</text>
      </view>
    </view>
  </view>

  <!-- 验收按钮 - 只有工单状态为已完成(2)且验收状态为待验收(0)时才显示 -->
  <view wx:if="{{detail && detail.status == 2 && detail.ys_status == 0}}" class="acceptance-buttons">
    <button class="accept-btn" bindtap="onAcceptance" data-result="1">验收通过</button>
    <button class="reject-btn" bindtap="onAcceptance" data-result="0">验收不通过</button>
  </view>
</view>
